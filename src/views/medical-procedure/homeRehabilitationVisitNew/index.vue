<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <template #search>
        <TBSearchContainer :is-show-toggle="true">
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="方案下达时间">
                <el-date-picker
                  v-model="timeRange"
                  unlink-panels
                  type="daterange"
                  :shortcuts="datePickerShortcuts"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  :clearable="false"
                  value-format="YYYY-MM-DD"
                  style="width: 250px"
                />
              </el-form-item>
              <el-form-item label="机构">
                <HospitalSelect v-model="queryParams.OrgIds" multiple />
              </el-form-item>
              <el-form-item label="科室">
                <DeptSelect
                  v-model="queryParams.DeptIds"
                  :org-id="
                    queryParams.OrgIds && queryParams.OrgIds.length === 1
                      ? queryParams.OrgIds[0]
                      : null
                  "
                  :disabled="!queryParams.OrgIds || queryParams.OrgIds.length !== 1"
                  multiple
                  style="width: 120px"
                />
              </el-form-item>
              <el-form-item label="下达人">
                <UserSelect
                  v-model="queryParams.PreCreatorIds"
                  :org-ids="queryParams.OrgIds"
                  :dept-ids="queryParams.DeptIds"
                  :role-types="['doctor', 'therapist', 'nurse']"
                  multiple
                  style="width: 120px"
                />
              </el-form-item>
              <el-form-item label="医助">
                <UserSelect
                  v-model="queryParams.AssistantIds"
                  :role-types="['assistant']"
                  multiple
                  style="width: 120px"
                />
              </el-form-item>
              <el-form-item label="是否测试数据">
                <el-select
                  v-model="queryParams.IsTest"
                  placeholder="请选择"
                  clearable
                  :empty-values="[null, undefined, '']"
                  :value-on-clear="() => null"
                  style="width: 80px"
                >
                  <el-option label="是" :value="1" />
                  <el-option label="否" :value="0" />
                </el-select>
              </el-form-item>
              <el-form-item label="关键字" prop="Keywords">
                <el-input
                  v-model="queryParams.Keywords"
                  placeholder="手机号/就诊号/患者姓名"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
          </template>
        </TBSearchContainer>
      </template>
      <template #table>
        <el-table
          ref="tableRef"
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          border
          row-key="Id"
          :height="tableFluidHeight"
          highlight-current-row
          style="text-align: center; flex: 1"
        >
          <el-table-column
            prop="PrescriptionId"
            label="就诊号"
            :show-overflow-tooltip="true"
            width="180"
            align="center"
          />
          <el-table-column label="患者" align="center">
            <template #default="scope">
              <div>{{ scope.row.PatName }} {{ scope.row.Age + "岁" }} {{ scope.row.PatSex }}</div>
              <div>{{ scope.row.PatPhone }}</div>
            </template>
          </el-table-column>
          <el-table-column label="方案" prop="PrescriptionName" align="center" />
          <el-table-column label="诊断" align="center">
            <template #default="scope">
              {{ scope.row.DiagnosisName }}
            </template>
          </el-table-column>
          <el-table-column prop="PreCreatorName" label="下达人" align="center" />
          <el-table-column prop="AssistantName" label="医助" align="center" />
          <el-table-column prop="OrganizationName" label="医院" align="center" />
          <el-table-column label="离院提醒" align="center">
            <template #default="scope">
              <div v-if="scope.row.LeaveHospitalObject">
                <p>{{ scope.row.LeaveHospitalObject.Content }}</p>
                <el-image
                  v-for="i in scope.row.LeaveHospitalObject.Imgs"
                  :key="i"
                  style="width: 50px; height: 50px; margin-right: 10px; margin-bottom: 10px"
                  :src="i"
                  preview-teleported
                  :preview-src-list="scope.row.LeaveHospitalObject.Imgs"
                />
              </div>
            </template>
          </el-table-column>
          <el-table-column label="医嘱执行跟进" align="center">
            <template #default="scope">
              <div v-if="scope.row.ExecObject">
                <p>{{ scope.row.ExecObject.Content }}</p>
                <el-image
                  v-for="i in scope.row.ExecObject.Imgs"
                  :key="i"
                  style="width: 50px; height: 50px; margin-right: 10px; margin-bottom: 10px"
                  :src="i"
                  preview-teleported
                  :preview-src-list="scope.row.ExecObject.Imgs"
                />
              </div>
            </template>
          </el-table-column>
          <el-table-column label="满意度回访" align="center">
            <template #default="scope">
              <div>
                <div>
                  <span v-if="scope.row.Myd">满意度：{{ scope.row.Myd }}</span>
                  <span v-if="scope.row.Zlxg">治疗效果：{{ scope.row.Zlxg }}</span>
                </div>
                <div v-if="scope.row.ReturnVisitEvaInputDtos">
                  <span v-for="i in scope.row.ReturnVisitEvaInputDtos" :key="i.Id">
                    {{ i.Name + ":" + i.Point }}
                  </span>
                </div>
                <div v-if="scope.row.ProEndObject">
                  <p v-if="scope.row.ProEndObject">{{ scope.row.ProEndObject.Content }}</p>
                  <el-image
                    v-for="i in scope.row.ProEndObject.Imgs"
                    :key="i"
                    style="width: 50px; height: 50px; margin-right: 10px; margin-bottom: 10px"
                    :src="i"
                    preview-teleported
                    :preview-src-list="scope.row.ProEndObject.Imgs"
                  />
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="IsTest" label="是否测试数据" align="center" width="100">
            <template #default="scope">
              {{ scope.row.IsTest }}
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.PageIndex"
          v-model:limit="queryParams.PageSize"
          @pagination="handleGetTableList"
        />
      </template>
    </BaseTableSearchContainer>
  </div>
</template>

<script setup lang="ts">
import Consult_Api from "@/api/consult";
import { ReturnVisitInputDTO, ReturnVisitItem } from "@/api/consult/types";
import { useTableConfig } from "@/hooks/useTableConfig";
import dayjs from "dayjs";
import { useDateRangePicker } from "@/hooks/useDateRangePicker";

const { datePickerShortcuts } = useDateRangePicker();

defineOptions({
  name: "HomeRehabilitationVisitNew",
});

interface PageReturnVisitItem extends ReturnVisitItem {
  LeaveHospitalObject?: any;
  ExecObject?: any;
  ProEndObject?: any;
  ContentObject?: any;
}

const queryParams = ref<ReturnVisitInputDTO>({
  OrgIds: null,
  DeptIds: null,
  AssistantIds: null,
  PreCreatorIds: null,
  Keywords: null,
  IsTest: 0,
  IsLoadCompleted: null,
  DtoName: "ReturnVisitRecordOutputDto",
  QueryStartDate: dayjs(new Date()).format("YYYY-MM-01") + " 00:00:00",
  QueryEndDate: dayjs(new Date()).format("YYYY-MM-DD") + " 23:59:59",
  Order: "ReturnVisitDateDesc",
  Scopeable: true,
  PageIndex: 1,
  PageSize: 20,
});
const timeRange = ref<[string, string]>([
  dayjs().format("YYYY-MM-01"),
  dayjs().format("YYYY-MM-DD"),
]);

const { tableLoading, pageData, total, tableRef, tableFluidHeight, tableResize } =
  useTableConfig<ReturnVisitItem>();

const handleQuery = () => {
  queryParams.value.PageIndex = 1;
  handleGetTableList();
};
const handleGetTableList = async () => {
  tableLoading.value = true;
  const res = await Consult_Api.getPreReturnVisitPage(queryParams.value);
  if (res.Type === 200) {
    const newData = res.Data.Data.map((s): PageReturnVisitItem => {
      return {
        ...s,
        ContentObject: s.Content ? JSON.parse(s.Content) : null,
        LeaveHospitalObject: s.LeaveHospital ? JSON.parse(s.LeaveHospital) : null,
        ExecObject: s.Exec ? JSON.parse(s.Exec) : null,
        ProEndObject: s.ProEnd ? JSON.parse(s.ProEnd) : null,
      };
    });
    pageData.value = newData;
    total.value = res.Data.TotalCount;
  }
  tableLoading.value = false;
};

watch(timeRange, (newVal) => {
  queryParams.value.QueryStartDate = dayjs(newVal[0]).format("YYYY-MM-DD 00:00:00");
  queryParams.value.QueryEndDate = dayjs(newVal[1]).format("YYYY-MM-DD 23:59:59");
});

onActivated(() => {
  handleGetTableList();
});
</script>

<style lang="scss" scoped></style>
