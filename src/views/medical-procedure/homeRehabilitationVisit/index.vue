<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <!-- 顶部筛选条件 -->
      <template #search>
        <TBSearchContainer :is-show-toggle="true">
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="医院">
                <HospitalSelect
                  v-model="queryParams.OrgIds"
                  :scopeable="true"
                  filterable
                  multiple
                  collapse-tags
                  clearable
                  @change="
                    () => {
                      queryParams.DeptIds = undefined;
                      queryParams.PreCreatorIds = undefined;
                    }
                  "
                />
              </el-form-item>
              <el-form-item label="科室">
                <DeptSelect
                  v-model="queryParams.DeptIds"
                  :org-id="queryParams.OrgIds?.[0]"
                  :disabled="queryParams.OrgIds?.length !== 1 || !queryParams.OrgIds?.[0]"
                  filterable
                  multiple
                  collapse-tags
                  clearable
                />
              </el-form-item>
              <el-form-item label="下达人">
                <UserSelect
                  v-model="queryParams.PreCreatorIds"
                  filterable
                  multiple
                  collapse-tags
                  clearable
                  placeholder="请选择下达人"
                  :org-ids="queryParams.OrgIds"
                  :dept-ids="queryParams.DeptIds"
                  :role-types="['nurse', 'therapist', 'doctor']"
                  :scopeable="true"
                />
              </el-form-item>
              <el-form-item label="医助">
                <UserSelect
                  v-model="queryParams.AssistantIds"
                  placeholder="请选择医助"
                  filterable
                  multiple
                  collapse-tags
                  clearable
                  :role-types="['assistant']"
                  :scopeable="false"
                />
              </el-form-item>
              <el-form-item label="状态">
                <KSelect
                  v-model="queryParams.IsLoadCompleted"
                  :data="[
                    { label: '未填写', value: 0 },
                    { label: '已填写', value: 1 },
                  ]"
                  :show-all="true"
                />
              </el-form-item>
              <el-form-item label="是否测试数据">
                <KSelect
                  v-model="queryParams.IsTest"
                  :data="[
                    { label: '否', value: 0 },
                    { label: '是', value: 1 },
                  ]"
                  :show-all="true"
                />
              </el-form-item>
              <el-form-item label="任务时间">
                <el-date-picker
                  v-model="dateRange"
                  type="daterange"
                  :shortcuts="datePickerShortcuts"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :clearable="false"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  unlink-panels
                  class="w-300px!"
                />
              </el-form-item>
              <el-form-item label="关键字">
                <el-input
                  v-model="queryParams.Keywords"
                  placeholder="手机号/就诊号/患者姓名"
                  clearable
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
          </template>
        </TBSearchContainer>
      </template>
      <!-- 列表 -->
      <template #table>
        <el-table
          :ref="kTableRef"
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          row-key="IdStr"
          :height="tableFluidHeight"
          :header-cell-style="{ textAlign: 'center' }"
          :cell-style="{ textAlign: 'center' }"
          border
          highlight-current-row
        >
          <el-table-column prop="PrescriptionId" label="就诊号" show-overflow-tooltip width="150" />
          <el-table-column label="患者" width="120">
            <template #default="scope">
              <el-text>
                {{ scope.row.PatName }} {{ scope.row.Age + "岁" }} {{ scope.row.PatSex }}
              </el-text>
              <el-text>{{ scope.row.PatPhone }}</el-text>
            </template>
          </el-table-column>
          <el-table-column prop="DiagnosisName" label="诊断" min-width="120" />
          <el-table-column prop="PreCreatorName" label="下达人" width="80" show-overflow-tooltip />
          <el-table-column prop="AssistantName" label="医助" width="100" show-overflow-tooltip />
          <el-table-column
            prop="OrganizationName"
            label="医院"
            min-width="120"
            show-overflow-tooltip
          />
          <el-table-column prop="Type" label="类型" width="110">
            <template #default="scope">
              {{
                ["", "离院提醒", "医嘱执行跟进", "第一次回访", "第二次回访", "满意度回访"][
                  scope.row.Type
                ]
              }}
            </template>
          </el-table-column>
          <el-table-column prop="ShowExternal" label="是否向医务人员展示" width="80">
            <template #default="scope">
              {{ scope.row.ShowExternal ? "是" : "否" }}
            </template>
          </el-table-column>
          <el-table-column label="回访结果" min-width="150">
            <template #default="scope">
              <el-text>{{ scope.row.Content?.Content }}</el-text>
            </template>
          </el-table-column>
          <el-table-column label="图片" width="140">
            <template #default="scope">
              <div v-if="scope.row.Content?.Imgs?.length" class="flex flex-wrap justify-center">
                <el-image
                  v-for="(url, index) in scope.row.Content.Imgs"
                  :key="index"
                  :src="url"
                  fit="cover"
                  class="w-50px h-50px m-5px"
                  preview-teleported
                  :preview-src-list="scope.row.Content.Imgs"
                  :initial-index="index"
                />
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="ReturnVisitDate"
            label="任务时间"
            :formatter="tableDateFormatDay"
            width="120"
          />
          <el-table-column
            prop="ContentTime"
            label="填写时间"
            :formatter="tableDateFormat"
            width="160"
          />
          <el-table-column label="状态" width="80">
            <template #default="scope">
              {{ scope.row.Content ? "已填写" : "未填写" }}
            </template>
          </el-table-column>
          <el-table-column prop="IsTest" label="是否测试数据" width="80" />
        </el-table>
      </template>
      <!-- 分页 -->
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.PageIndex"
          v-model:limit="queryParams.PageSize"
          @pagination="requestTableList"
        />
      </template>
    </BaseTableSearchContainer>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import { useTableConfig } from "@/hooks/useTableConfig";
import { ReturnVisitContentDTO, ReturnVisitInputDTO, ReturnVisitItem } from "@/api/consult/types";
import Consult_Api from "@/api/consult";
import { useDateRangePicker } from "@/hooks/useDateRangePicker";

const { datePickerShortcuts } = useDateRangePicker();

interface ReturnVisitItemShow extends Omit<ReturnVisitItem, "Content"> {
  Content?: ReturnVisitContentDTO;
}

// 调试开关
const kEnableDebug = false;
defineOptions({
  name: "HomeRehabilitationVisit",
});

const {
  kTableRef,
  pageData,
  tableLoading,
  tableFluidHeight,
  total,
  tableResize,
  tableDateFormat,
  tableDateFormatDay,
} = useTableConfig<ReturnVisitItemShow>();

// 查询条件
const queryParams = reactive<ReturnVisitInputDTO>({
  QueryStartDate: dayjs().startOf("month").format("YYYY-MM-DD HH:mm:ss"),
  QueryEndDate: dayjs().endOf("day").format("YYYY-MM-DD HH:mm:ss"),
  IsTest: 0,
  DtoName: "ReturnVisitRecordOutputDto",
  Order: "ReturnVisitDateDesc",
  Scopeable: true,
  PageIndex: 1,
  PageSize: 20,
});

// 定义 dateRange
const dateRange = computed({
  get() {
    // 从 queryParams 中获取日期范围
    return [queryParams.QueryStartDate, queryParams.QueryEndDate];
  },
  set(newValue) {
    // 当用户选择日期范围时，更新 queryParams
    if (newValue && newValue.length === 2) {
      queryParams.QueryStartDate = newValue[0].split(" ")[0] + " 00:00:00";
      queryParams.QueryEndDate = newValue[1].split(" ")[0] + " 23:59:59";
    }
  },
});

// 点击搜索
function handleQuery() {
  queryParams.PageIndex = 1;
  requestTableList();
}

// 请求列表数据
async function requestTableList() {
  tableLoading.value = true;
  const r = await Consult_Api.getReturnVisitRecords(queryParams);
  tableLoading.value = false;
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
    return;
  }

  // 请求成功
  pageData.value = r.Data.Data.map((item) => ({
    ...item,
    Content: item.Content ? JSON.parse(item.Content) : undefined,
  }));
  total.value = r.Data.Total;
}

onActivated(() => {
  requestTableList();
});
</script>

<style lang="scss" scoped></style>
